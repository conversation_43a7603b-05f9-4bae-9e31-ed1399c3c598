import * as api from '../../../../services/api';
import { defaultApiConfig, generateBaseUrl } from '../../../../config/apiConfig';
import {
  ChatbotQuestion,
  ChatbotApiResponse
} from './models';

const getBaseUrl = () => generateBaseUrl(defaultApiConfig, '');

// Step 1: Create chatbot basic information (POST /v1/chatbot)
export const createChatbotStep1 = (step1Data: {
  name: string;
  type: string;
  description: string;
  welcomeMessage: string;
  thankYouMessage: string;
}): Promise<ChatbotApiResponse> => {
  const headers = api.setHeaders();
  const baseUrl = getBaseUrl();
  const url = `${baseUrl}/chatbot`;

  const payload = {
    name: step1Data.name,
    type: step1Data.type,
    description: step1Data.description,
    welcomeMessage: step1Data.welcomeMessage,
    thankYouMessage: step1Data.thankYouMessage
  };

  console.log('Making HTTP POST request to:', url);
  console.log('Request payload:', payload);
  console.log('Request headers:', headers);

  const requestConfig = {
    url,
    headers,
    method: 'post',
    data: payload
  };

  return api.callAPI(requestConfig)
    .then(response => {
      console.log('HTTP POST response received:', response);
      // Map axios response to expected ChatbotApiResponse format
      return {
        data: response.data,
        message: response.data?.message || 'Chatbot created successfully',
        success: response.status >= 200 && response.status < 300
      };
    })
    .catch(error => {
      console.error('HTTP POST request failed:', error);
      throw error;
    });
};

// Step 2: Submit questions to chatbot (POST /chatbot/{chatbotId}/questions)
export const submitChatbotQuestions = (chatbotId: string, questions: ChatbotQuestion[]): Promise<ChatbotApiResponse> => {
  const headers = api.setHeaders();
  const baseUrl = getBaseUrl();
  const url = `${baseUrl}/chatbot/${chatbotId}/questions`;

  // Filter out empty questions and prepare payload
  const validQuestions = questions.filter(q => q.question && q.question.trim() !== '');
  const payload = validQuestions.map((question, index) => ({
    question: question.question.trim(),
    order: index + 1
  }));

  console.log('Making HTTP POST request to:', url);
  console.log('Questions payload:', payload);
  console.log('Request headers:', headers);

  const requestConfig = {
    url,
    headers,
    method: 'post',
    data: payload
  };

  return api.callAPI(requestConfig)
    .then(response => {
      console.log('HTTP POST questions response received:', response);
      // Map axios response to expected ChatbotApiResponse format
      return {
        data: response.data,
        message: response.data?.message || 'Questions submitted successfully',
        success: response.status >= 200 && response.status < 300
      };
    })
    .catch(error => {
      console.error('HTTP POST questions request failed:', error);
      throw error;
    });
};

// Step 3: Upload knowledge base to chatbot (POST /v1/chatbot/{id}/knowledgebase)
export const uploadKnowledgeBase = (chatbotId: string, files: File[]): Promise<ChatbotApiResponse> => {
  const headers = api.setHeaders();
  const baseUrl = getBaseUrl();
  const url = `${baseUrl}/chatbot/${chatbotId}/knowledgebase`;

  console.log('Making HTTP POST request to:', url);
  console.log('Uploading files:', files.map(f => f.name));
  console.log('Request headers:', headers);

  const formData = new FormData();
  files.forEach((file, index) => {
    formData.append(`files[${index}]`, file);
  });

  return api.callAPI({
    url,
    headers,
    method: 'post',
    data: formData
  })
    .then(response => {
      console.log('HTTP POST knowledge base response received:', response);
      return {
        data: response.data,
        message: 'Knowledge base uploaded successfully',
        success: true
      };
    })
    .catch(error => {
      console.error('HTTP POST knowledge base request failed:', error);
      return {
        data: null,
        message: `Failed to upload knowledge base: ${error.message}`,
        success: false
      };
    });
};
