@import 'src/assets/styles/scss/base/variables';

.chatbot-form-container {
  // Step content styling
  .step-content {
    background-color: $white;
    border-radius: 0.5rem;
    padding: 2rem;
    margin-top: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid $custom-border-color;

    // Scrollable step content for step 2 (questions)
    &.step-content-scrollable {
      max-height: calc(100vh - 350px); // Adjust based on header + footer space
      overflow-y: auto;
      padding-bottom: 1rem;
    }
  }

  // Form actions styling - fixed at bottom
  .form-actions {
    background-color: $gray-100;
    border-top: 1px solid $custom-border-color;
    padding: 1.5rem 2rem;
    margin: 2rem -2rem -2rem -2rem;
    border-radius: 0 0 0.5rem 0.5rem;
    position: sticky;
    bottom: 0;
    z-index: 20;

    .btn {
      min-width: 7rem;
      font-weight: 500;

      &.btn-outline-primary {
        border-color: $primary;
        color: $primary;

        &:hover {
          background-color: $primary;
          color: $white;
        }
      }

      &.btn-primary {
        background-color: $primary;
        border-color: $primary;

        &:hover {
          background-color: darken($primary, 10%);
          border-color: darken($primary, 10%);
        }
      }
    }
  }

  // Import-style step progress indicator - exact match with Import approach
  .chatbot.step-progress-wrapper,
  .step-progress-wrapper.chatbot {
    border-bottom: 0;

    .import-step-progress-element {
      flex: 1;
      position: relative;
      text-align: center;
      transition: all 300ms cubic-bezier(0.2, 0, 0, 1) 0s;

      &:not(:first-child)::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 1px;
        background-color: $custom-border-color;
        top: 0.8rem;
        transition: all 300ms cubic-bezier(0.2, 0, 0, 1) 0s;
        right: 50%;
      }

      &.completed {
        .step-progress-count {
          background-color: $primary;
          color: $white;
        }
        &:not(:first-child)::before {
          background-color: $primary;
        }

        &:not(:last-child) .import-step-progress-bar {
          position: absolute;
          height: 1px;
          background-color: $primary;
          top: 0.8rem;
          transition: all 300ms cubic-bezier(0.2, 0, 0, 1) 0s;
          left: 50%;
          z-index: 1;
        }
      }

      // Active step should show line to previous step as completed
      &.active {
        .step-progress-count {
          background-color: $primary;
          color: $white;
        }
        &:not(:first-child)::before {
          background-color: $primary;
        }
      }

      .step-progress-count {
        border: 1.5px solid $blue;
        color: $blue;
        padding-top: 0;
        line-height: 1.7333rem;
      }
      .step-progress-name {
        color: $table-head-color;
        cursor: default;
        font-size: 12px;
        font-weight: 500;
      }
    }
  }
  
  .step-content {
    background: $gray-100;
    padding: 30px;
    border-radius: 8px;
    margin-bottom: 20px;

    h3 {
      font-size: 1rem;
      font-weight: 500;
      letter-spacing: 0.0293rem;
      margin-bottom: 1.333rem;
      color: $dark;
    }

    p {
      font-size: 0.9rem;
      font-weight: normal;
      line-height: 1.333rem;
      color: $gray-900;
      margin-bottom: 1rem;
    }

    .form-label {
      font-size: 0.8rem;
      font-weight: 500;
      color: $gray-900;
      margin-bottom: 0.5rem;
    }

    // Knowledge Base Upload styling - matching Import file upload
    .knowledge-base-upload {
      .form-control.select-file, .form-control.upload-file {
        height: calc(1.5em + 1.6rem);
        width: 75%;
        line-height: 1.8;
        color: $gray-600;
      }

      .form-control.upload-file {
        background-color: $gray-200;
        .selected-file {
          background-color: $gray-350;
          color: $black;
          font-size: 0.9rem;
          padding: 0.3rem 0.5rem;
          margin-left: -0.25rem;
        }
      }

      .form-control span.select-file-placeholder {
        opacity: 0.5;
        padding-top: 0.25rem;
      }

      input[type="file"] {
        display: none;
      }

      .select-file-button {
        width: 7rem;
        height: 2.25rem;
        float: right;
      }

      .cancel-file-wrapper {
        cursor: pointer;
        font-size: 0.8rem;
        color: $gray-650;
      }

      // Multiple files support styling
      .selected-files-container {
        .selected-files-header {
          .selected-files-count {
            font-size: 0.9rem;
            font-weight: 500;
            color: $dark;
          }
        }

        .selected-files-list {
          max-height: 200px;
          overflow-y: auto;
          border: 1px solid $custom-border-color;
          border-radius: 0.25rem;
          padding: 0.5rem;
          background-color: $gray-100;

          .selected-file-item {
            padding: 0.5rem;
            background-color: $white;
            border: 1px solid $custom-border-color;
            border-radius: 0.25rem;
            margin-bottom: 0.5rem;

            &:last-child {
              margin-bottom: 0;
            }

            .file-name {
              font-size: 0.85rem;
              color: $dark;
              flex: 1;

              i {
                color: $danger;
              }

              small {
                font-size: 0.75rem;
              }
            }

            .btn {
              padding: 0.25rem 0.5rem;
              font-size: 0.75rem;
            }
          }
        }
      }

      // Knowledge Base Information Section
      .knowledge-base-info {
        .info-section {
          .info-title {
            font-size: 0.9rem;
            font-weight: 500;
            color: $dark;
            margin-bottom: 0.5rem;
          }

          .info-description {
            font-size: 0.85rem;
            line-height: 1.4;
            color: $gray-900;
            margin-bottom: 0;
          }
        }

        .disclaimer-section {
          .alert {
            border-left: 4px solid $warning;
            background-color: lighten($warning, 45%);
            border-color: lighten($warning, 30%);

            .disclaimer-title {
              font-size: 0.9rem;
              font-weight: 600;
              color: darken($warning, 20%);

              i {
                color: $warning;
              }
            }

            .disclaimer-content {
              font-size: 0.8rem;
              line-height: 1.4;
              color: $gray-900;

              p {
                margin-bottom: 0.75rem;

                &:last-child {
                  margin-bottom: 0;
                }
              }

              strong {
                font-weight: 600;
                color: $dark;
              }
            }
          }
        }
      }

      // Terms Acceptance Section
      .terms-acceptance {
        .form-check {
          .form-check-input {
            margin-top: 0.2rem;
          }

          .form-check-label {
            font-size: 0.85rem;
            line-height: 1.4;
            color: $gray-900;
            margin-left: 0.5rem;

            strong {
              font-weight: 600;
              color: $dark;
            }
          }
        }
      }
    }

    // Questions Field Array Optimization
    .questions-field-array {
      .questions-header {
        position: sticky;
        top: 0;
        background-color: $white;
        z-index: 10;
        padding-bottom: 1rem;
        margin-bottom: 1rem !important;
        border-bottom: 1px solid $custom-border-color;

        h5 {
          font-size: 1rem;
          font-weight: 500;
          letter-spacing: 0.0293rem;
          margin-bottom: 0.5rem;
          color: $dark;

          .badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
          }
        }

        p {
          font-size: 0.85rem;
          line-height: 1.3;
          margin-bottom: 0;
        }
      }

      .questions-content {
        .question-item {
          margin-bottom: 1rem !important;

          .card {
            border: 1px solid $custom-border-color;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

            .card-body {
              padding: 1rem;

              .card-title {
                font-size: 0.9rem;
                font-weight: 500;
                color: $dark;
              }

              .form-group {
                margin-bottom: 0;

                label {
                  font-size: 0.8rem;
                  font-weight: 500;
                  margin-bottom: 0.5rem;
                }

                textarea {
                  min-height: 48px; // Two lines height
                  max-height: 48px; // Prevent expansion
                  height: 48px; // Fixed height
                  resize: none; // Disable resize
                  overflow-y: auto; // Allow scrolling if text exceeds
                }
              }
            }
          }
        }

        .questions-empty-state {
          padding: 2rem 1rem !important;

          i {
            color: $gray-500 !important;
          }

          h6, p {
            color: $gray-600 !important;
          }
        }
      }
    }
  }
}

  // Questions Field Array - Professional Design
  .questions-field-array {
    .questions-header {
      h5 {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 0.5rem;
      }

      .text-muted {
        font-size: 0.875rem;
        line-height: 1.4;
      }

      .btn-outline-primary {
        border-color: #007bff;
        color: #007bff;
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;

        &:hover {
          background-color: #007bff;
          color: white;
        }

        i {
          font-size: 0.75rem;
        }
      }
    }

    .questions-empty-state {
      background-color: #f8f9fa;
      border: 2px dashed #dee2e6;
      border-radius: 0.5rem;
      padding: 3rem 2rem;

      .fas.fa-question-circle {
        color: #6c757d;
      }

      h6 {
        color: #495057;
        font-weight: 600;
      }

      .btn-primary {
        padding: 0.5rem 1.5rem;
        font-weight: 500;
      }
    }

    .question-item {
      .card {
        border: 1px solid #e3e6f0;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          border-color: #007bff;
        }

        .card-body {
          padding: 1.5rem;

          // Ensure TextArea takes full width
          .w-100 {
            width: 100% !important;

            .form-group {
              margin-bottom: 0;
              width: 100%;

              .form-control {
                width: 100% !important;
              }
            }
          }
        }

        .card-title {
          color: #2c3e50;
          font-weight: 600;
          font-size: 1rem;

          .fas.fa-question-circle {
            color: #007bff;
          }
        }
      }

      .question-actions {
        .btn {
          padding: 0.25rem 0.5rem;
          font-size: 0.75rem;
          border-radius: 0.25rem;

          &.btn-outline-danger {
            border-color: #dc3545;
            color: #dc3545;

            &:hover {
              background-color: #dc3545;
              color: white;
            }
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
      }
    }

    .questions-summary {
      .alert {
        border: 1px solid #b8daff;
        background-color: #d1ecf1;
        color: #0c5460;
        border-radius: 0.5rem;

        .fas.fa-info-circle {
          color: #17a2b8;
        }

        strong {
          font-weight: 600;
        }

        .small {
          font-size: 0.875rem;
          margin-top: 0.25rem;
          opacity: 0.8;
        }
      }
    }

    .alert-danger {
      border-color: #f5c6cb;
      background-color: #f8d7da;
      color: #721c24;

      .fas.fa-exclamation-triangle {
        color: #dc3545;
      }
    }
  }

.knowledge-base-upload {
  .upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      border-color: #007bff;
      background-color: #f8f9fa;
    }
    
    &.drag-over {
      border-color: #007bff;
      background-color: #e3f2fd;
    }
    
    .upload-icon {
      font-size: 48px;
      color: #6c757d;
    }
    
    .upload-requirements {
      font-size: 12px;
      line-height: 1.4;
    }
  }
  
  .existing-knowledge-base {
    .alert {
      border-left: 4px solid #28a745;
    }
  }
  
  .selected-file {
    .card {
      border: 1px solid #28a745;
      background-color: #f8fff9;
    }
  }
}

.whatsapp-chatbots-list {
  .chatbot-status {
    &.active {
      color: $success;
      font-weight: 600;
    }

    &.inactive {
      color: $danger;
      font-weight: 600;
    }
  }

  .chatbot-type {
    &.ai {
      color: $primary;
      font-weight: 600;
    }

    &.rule-based {
      color: $electric;
      font-weight: 600;
    }
  }
}

// Additional styling for better UX
.chatbot-form-container {
  .form-actions {
    background: $gray-100;
    padding: 20px;
    border-radius: 8px;
    margin-top: 30px;
    border: 1px solid $custom-border-color;

    .btn.btn-outline-primary {
      border-color: $primary;
      color: $primary;

      &:hover {
        background-color: $primary;
        color: $white;
      }
    }
  }

  .step-content {
    min-height: 400px;

    .form-group {
      margin-bottom: 20px;
    }

    .required-field::after {
      content: " *";
      color: $danger;
    }

    h3 {
      font-size: 1rem;
      font-weight: 500;
      letter-spacing: 0.0293rem;
      margin-bottom: 1.333rem;
      color: $dark;
    }

    p {
      font-size: 0.9rem;
      font-weight: normal;
      line-height: 1.333rem;
      color: $gray-900;
    }
  }
}

.questions-field-array {
  .empty-state {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px;
    margin: 20px 0;
  }

  .questions-header {
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-bottom: 20px;
  }
}

.knowledge-base-upload {
  .upload-area {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;

    .upload-content {
      max-width: 400px;
    }
  }

  .file-info {
    display: flex;
    align-items: center;
    gap: 10px;

    .file-icon {
      font-size: 24px;
      color: #dc3545;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .chatbot-form-container {
    .step-indicator {
      .step {
        .step-number {
          width: 30px;
          height: 30px;
          font-size: 12px;
        }

        .step-title {
          font-size: 12px;
        }
      }
    }

    .step-content {
      padding: 20px;
      min-height: auto;
    }
  }

  .questions-field-array {
    .question-actions {
      .btn {
        padding: 2px 6px;
        font-size: 10px;
      }
    }
  }

  // Global TextArea styling for chatbot forms
  .form-control {
    &.w-100 {
      width: 100% !important;
      min-width: 100%;
      max-width: 100%;
    }
  }

  // Ensure all form elements take full width in question cards
  .question-item {
    .card-body {
      > div {
        width: 100%;

        .form-group {
          width: 100%;
          margin-bottom: 0;

          label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
          }

          input[type="text"], textarea {
            width: 100% !important;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            padding: 0.75rem;
            font-size: 0.875rem;
            line-height: 1.5;

            &:focus {
              border-color: #007bff;
              box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
              outline: 0;
            }

            &::placeholder {
              color: #6c757d;
              opacity: 1;
            }
          }

          textarea {
            resize: vertical;
          }
        }
      }
    }
  }
}
