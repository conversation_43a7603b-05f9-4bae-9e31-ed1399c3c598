import * as React from 'react';
import { shallow } from 'enzyme';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import configureStore from 'redux-mock-store';
import ChatbotFormLayout from './CreateEdit/ChatbotFormLayout';
import { EntityFormAction } from '../../formLayout/models/Form';
import * as service from './service';

// Mock all service functions
const mockUploadKnowledgeBase = jest.fn();
const mockCreateChatbotStep1 = jest.fn();
const mockSubmitChatbotQuestions = jest.fn();

jest.mock('./service', () => ({
  createChatbotStep1: mockCreateChatbotStep1,
  submitChatbotQuestions: mockSubmitChatbotQuestions,
  uploadKnowledgeBase: mockUploadKnowledgeBase
}));

const mockStore = configureStore([]);

describe('Chatbot Flow Integration Tests', () => {
  let store: any;

  beforeEach(() => {
    jest.clearAllMocks();

    store = mockStore({
      form: {
        chatbotForm: {
          values: {
            name: 'Test Chatbot',
            description: 'Test Description',
            type: 'AI',
            welcomeMessage: 'Welcome to our chatbot!',
            thankYouMessage: 'Thank you for using our service!',
            questions: [
              { question: 'What are your business hours?', order: 1 },
              { question: 'How can I contact support?', order: 2 }
            ]
          }
        }
      },
      appData: {
        profilePermissions: [
          {
            name: 'whatsappBusiness',
            action: {
              read: true,
              write: true,
              update: true,
              delete: false,
              readAll: true,
              updateAll: true
            }
          }
        ],
        currentUserId: 'user123'
      }
    });
  });

  it('should render without crashing', () => {
    const props = {
      action: EntityFormAction.CREATE,
      history: {
        push: jest.fn(),
        goBack: jest.fn()
      },
      location: {
        pathname: '/setup/whatsapp-business/chatbots/create'
      }
    };

    const wrapper = shallow(
      <Provider store={store}>
        <BrowserRouter>
          <ChatbotFormLayout {...props} />
        </BrowserRouter>
      </Provider>
    );

    expect(wrapper.length).toBe(1);
  });

  it('should have correct service mocks', () => {
    expect(service.createChatbotStep1).toBeDefined();
    expect(service.submitChatbotQuestions).toBeDefined();
    expect(service.uploadKnowledgeBase).toBeDefined();
  });

  describe('Multiple Knowledge Base File Upload', () => {
    const mockChatbotId = 'test-chatbot-123';

    beforeEach(() => {
      mockUploadKnowledgeBase.mockResolvedValue({
        data: { success: true, message: 'Files uploaded successfully' }
      });
    });

    it('should handle multiple file upload API call', async () => {
      const mockFiles = [
        new File(['content1'], 'document1.pdf', { type: 'application/pdf' }),
        new File(['content2'], 'document2.pdf', { type: 'application/pdf' }),
        new File(['content3'], 'document3.pdf', { type: 'application/pdf' })
      ];

      // Mock FormData
      const mockFormData = new FormData();
      mockFiles.forEach((file, index) => {
        mockFormData.append(`file_${index}`, file);
      });

      // Test API call with multiple files
      await mockUploadKnowledgeBase(mockChatbotId, mockFormData);

      expect(mockUploadKnowledgeBase).toHaveBeenCalledWith(mockChatbotId, mockFormData);
      expect(mockUploadKnowledgeBase).toHaveBeenCalledTimes(1);
    });

    it('should handle API endpoint construction for knowledge base upload', () => {
      const chatbotId = 'chatbot-456';
      const expectedEndpoint = `/v1/chatbot/${chatbotId}/knowledgebase`;

      // Verify the API endpoint format matches the specification
      expect(expectedEndpoint).toBe('/v1/chatbot/chatbot-456/knowledgebase');
      expect(expectedEndpoint).toMatch(/^\/v1\/chatbot\/[^\/]+\/knowledgebase$/);
    });

    it('should handle upload success response', async () => {
      const mockSuccessResponse = {
        data: {
          success: true,
          message: 'Knowledge base files uploaded successfully',
          uploadedFiles: [
            { name: 'doc1.pdf', size: 1024, status: 'uploaded' },
            { name: 'doc2.pdf', size: 2048, status: 'uploaded' }
          ]
        }
      };

      mockUploadKnowledgeBase.mockResolvedValue(mockSuccessResponse);

      const result = await mockUploadKnowledgeBase(mockChatbotId, new FormData());

      expect(result.data.success).toBe(true);
      expect(result.data.uploadedFiles).toHaveLength(2);
      expect(result.data.uploadedFiles[0].status).toBe('uploaded');
    });

    it('should handle upload error response', async () => {
      const mockErrorResponse = {
        response: {
          data: {
            error: 'File upload failed',
            details: 'One or more files could not be processed'
          }
        }
      };

      mockUploadKnowledgeBase.mockRejectedValue(mockErrorResponse);

      try {
        await mockUploadKnowledgeBase(mockChatbotId, new FormData());
      } catch (error: any) {
        expect(error.response.data.error).toBe('File upload failed');
        expect(error.response.data.details).toBe('One or more files could not be processed');
      }
    });

    it('should validate file types and sizes before upload', () => {
      const validFile = new File(['content'], 'valid.pdf', { type: 'application/pdf' });
      const invalidFile = new File(['content'], 'invalid.txt', { type: 'text/plain' });
      const largeFile = new File(['x'.repeat(26 * 1024 * 1024)], 'large.pdf', { type: 'application/pdf' });

      // Test file validation logic
      expect(validFile.name.endsWith('.pdf')).toBe(true);
      expect(invalidFile.name.endsWith('.pdf')).toBe(false);
      expect(largeFile.size > 25 * 1024 * 1024).toBe(true);
    });

    it('should support batch file processing', () => {
      const files = [
        new File(['1'], 'doc1.pdf', { type: 'application/pdf' }),
        new File(['2'], 'doc2.pdf', { type: 'application/pdf' }),
        new File(['3'], 'doc3.pdf', { type: 'application/pdf' })
      ];

      // Test batch processing capabilities
      const formData = new FormData();
      files.forEach((file, index) => {
        formData.append(`files[${index}]`, file);
      });

      // Verify FormData can handle multiple files
      expect(files.length).toBe(3);
      expect(files.every(file => file.type === 'application/pdf')).toBe(true);
    });
  });
});
